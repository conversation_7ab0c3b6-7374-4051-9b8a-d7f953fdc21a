using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Results;
using Core.Utilities.Paging;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class MembershipTypeManager : IMembershipTypeService
    {
        IMembershiptypeDal _membershipTypeDal;
        private readonly ICompanyContext _companyContext;

        public MembershipTypeManager(IMembershiptypeDal membershipTypeDal, ICompanyContext companyContext)
        {
            _membershipTypeDal = membershipTypeDal;
            _companyContext = companyContext;
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("MembershipType")]
        [ValidationAspect(typeof(MembershipTypeValidator))]
        public IResult Add(MembershipType membershipType)
        {
            _membershipTypeDal.Add(membershipType);
            return new SuccessResult(Messages.MembershipTypeAdded);
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [SmartCacheRemoveAspect("MembershipType")]
        public IResult Delete(int id)
        {
            // Silinecek MembershipType'ı al - CompanyID kontrolü ile güvenli erişim
            var membershipType = _membershipTypeDal.Get(mt => mt.MembershipTypeID == id && mt.CompanyID == _companyContext.GetCompanyId());
            if (membershipType == null)
            {
                return new ErrorResult("Üyelik türü bulunamadı veya erişim yetkiniz yok.");
            }

            // Soft delete yap
            membershipType.IsActive = false;
            membershipType.DeletedDate = DateTime.Now;
            _membershipTypeDal.Update(membershipType);

            return new SuccessResult(Messages.MembershipTypeDeleted);
        }
        [SecuredOperation("owner,admin")]
        [MultiTenantCacheAspect(duration: 120, "MembershipType", "Configuration")]
        public IDataResult<List<MembershipType>> GetAll()
        {
            return new SuccessDataResult<List<MembershipType>>(_membershipTypeDal.GetAll(m => m.IsActive == true));
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<PaginatedResult<MembershipType>> GetAllPaginated(MembershipTypePagingParameters parameters)
        {
            var result = _membershipTypeDal.GetAllPaginated(parameters);
            return new SuccessDataResult<PaginatedResult<MembershipType>>(result);
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("MembershipType")]
        [ValidationAspect(typeof(MembershipTypeValidator))]
        public IResult Update(MembershipType membershipType)
        {
            // Mevcut MembershipType'ı al - CompanyID kontrolü ile güvenli erişim
            var existingMembershipType = _membershipTypeDal.Get(mt => mt.MembershipTypeID == membershipType.MembershipTypeID && mt.CompanyID == _companyContext.GetCompanyId());
            if (existingMembershipType == null)
            {
                return new ErrorResult("Üyelik türü bulunamadı veya erişim yetkiniz yok.");
            }

            // Güvenlik için CompanyID'yi tekrar ata ve CreationDate'i koru
            membershipType.CompanyID = _companyContext.GetCompanyId();
            membershipType.CreationDate = existingMembershipType.CreationDate;
            membershipType.UpdatedDate = DateTime.Now;

            _membershipTypeDal.Update(membershipType);
            return new SuccessResult(Messages.MembershipTypeUpdated);
        }
        [PerformanceAspect(3)]
        [SecuredOperation("owner,admin")]
        [MultiTenantCacheAspect(duration: 120, "MembershipType", "Branch")]
        public IDataResult<List<BranchGetAllDto>> GetBranchesAndTypes()
        {
            var result = _membershipTypeDal.GetAll(m => m.IsActive == true).Select(mt => new BranchGetAllDto  // Sadece aktif olanları getir
            {
                MembershipTypeID = mt.MembershipTypeID,
                Branch = mt.Branch,
                TypeName = mt.TypeName
            }).ToList();

            return new SuccessDataResult<List<BranchGetAllDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 60, "MembershipType", "PackagesByBranch")]
        public IDataResult<List<PackageWithCountDto>> GetPackagesByBranch(string branch)
        {
            return new SuccessDataResult<List<PackageWithCountDto>>(_membershipTypeDal.GetPackagesByBranch(branch));
        }
    }
}
